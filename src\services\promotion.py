def calculate_duration_in_grade(hire_date, last_promotion_date):
    from datetime import datetime
    today = datetime.now()
    if last_promotion_date:
        duration = today - last_promotion_date
    else:
        duration = today - hire_date
    return duration.days // 365  # Return duration in full years

def is_eligible_for_promotion(employee):
    min_promotion_years = 4
    duration_in_grade = calculate_duration_in_grade(employee.hire_date, employee.last_promotion_date)
    
    if duration_in_grade < min_promotion_years:
        return False

    performance_ratings = employee.performance_ratings[-3:]  # Last 3 years
    if all(rating >= 3 for rating in performance_ratings):  # Assuming 3 is the minimum rating for eligibility
        return True
    elif any(rating >= 4 for rating in performance_ratings):  # At least one rating of 4 or higher
        return 'conditionally eligible'
    
    return False

def categorize_employees(employees):
    eligible = []
    conditionally_eligible = []
    not_eligible = []

    for employee in employees:
        eligibility = is_eligible_for_promotion(employee)
        if eligibility is True:
            eligible.append(employee)
        elif eligibility == 'conditionally eligible':
            conditionally_eligible.append(employee)
        else:
            not_eligible.append(employee)

    return {
        'eligible': eligible,
        'conditionally_eligible': conditionally_eligible,
        'not_eligible': not_eligible
    }