from datetime import datetime
import pandas as pd
from src.models.employee import Employee

def generate_report(employees):
    eligible = []
    conditionally_eligible = []
    not_eligible = []

    for employee in employees:
        if employee.is_eligible_for_promotion():
            eligible.append(employee)
        elif employee.is_conditionally_eligible():
            conditionally_eligible.append(employee)
        else:
            not_eligible.append(employee)

    return {
        "eligible": eligible,
        "conditionally_eligible": conditionally_eligible,
        "not_eligible": not_eligible
    }

def search_employees(employees, search_term):
    return [emp for emp in employees if emp.name.lower() == search_term.lower() or emp.employee_id == search_term]

def export_to_excel(report, filename):
    with pd.ExcelWriter(filename) as writer:
        for category, emp_list in report.items():
            df = pd.DataFrame([emp.to_dict() for emp in emp_list])
            df.to_excel(writer, sheet_name=category, index=False)

def export_to_pdf(report, filename):
    from fpdf import FPDF

    pdf = FPDF()
    pdf.set_auto_page_break(auto=True, margin=15)
    pdf.add_page()
    pdf.set_font("Arial", size=12)

    for category, emp_list in report.items():
        pdf.cell(200, 10, category.capitalize(), ln=True, align='C')
        for emp in emp_list:
            pdf.cell(200, 10, f"{emp.name} (ID: {emp.employee_id})", ln=True)

    pdf.output(filename)