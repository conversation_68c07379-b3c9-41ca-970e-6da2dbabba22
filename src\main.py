# employee-promotion-management/src/main.py

import csv
from datetime import datetime
from models.employee import Employee
from services.promotion import evaluate_promotion
from services.report import generate_report
from utils.date_utils import calculate_years, days_since_last_promotion

def load_employees(file_path):
    employees = []
    with open(file_path, mode='r') as file:
        reader = csv.DictReader(file)
        for row in reader:
            employee = Employee(
                name=row['name'],
                employee_id=row['id'],
                hire_date=datetime.strptime(row['hire_date'], '%Y-%m-%d'),
                last_promotion_date=datetime.strptime(row['last_promotion_date'], '%Y-%m-%d'),
                current_grade=row['current_grade'],
                performance_ratings=list(map(int, row['performance_ratings'].split(','))),
                administrative_status=row['administrative_status']
            )
            employees.append(employee)
    return employees

def main():
    employees = load_employees('src/data/employees.csv')
    
    eligible = []
    conditionally_eligible = []
    not_eligible = []

    for employee in employees:
        if evaluate_promotion(employee):
            eligible.append(employee)
        elif employee.is_conditionally_eligible():
            conditionally_eligible.append(employee)
        else:
            not_eligible.append(employee)

    report = generate_report(eligible, conditionally_eligible, not_eligible)
    
    print(report)

if __name__ == "__main__":
    main()