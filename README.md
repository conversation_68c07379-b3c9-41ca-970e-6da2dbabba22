# Employee Promotion Management

This project is designed to manage employee promotions within an organization. It includes features for calculating promotion eligibility based on various criteria such as duration in the current grade, performance ratings, and administrative status.

## Features

- **Employee Management**: Maintain employee records including name, ID, hire date, last promotion date, current grade, performance ratings, and administrative status.
- **Promotion Eligibility Evaluation**: Determine if employees are eligible, conditionally eligible, or not eligible for promotion based on:
  - Duration in the current grade (minimum of 4 years required).
  - Performance ratings over the last 3 years.
- **Reporting**: Generate categorized reports of employees based on their promotion eligibility.
- **Search Functionality**: Search for employees by name or ID.
- **Export Options**: Export reports to Excel or PDF formats.

## Project Structure

```
employee-promotion-management
├── src
│   ├── main.py                # Entry point of the application
│   ├── models
│   │   └── employee.py        # Employee class definition
│   ├── services
│   │   ├── promotion.py       # Promotion evaluation logic
│   │   └── report.py          # Report generation functions
│   ├── utils
│   │   └── date_utils.py      # Date utility functions
│   ├── data
│   │   └── employees.csv       # Sample employee data
│   └── types
│       └── __init__.py        # Custom types or interfaces
├── requirements.txt           # Project dependencies
└── README.md                  # Project documentation
```

## Setup Instructions

1. Clone the repository:
   ```
   git clone <repository-url>
   cd employee-promotion-management
   ```

2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Run the application:
   ```
   python src/main.py
   ```

## Usage Guidelines

- Upon running the application, follow the prompts to input employee data and generate reports.
- Use the search functionality to find specific employees by their name or ID.
- Reports can be exported in your preferred format (Excel or PDF).

## Contributing

Contributions are welcome! Please feel free to submit a pull request or open an issue for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for more details.