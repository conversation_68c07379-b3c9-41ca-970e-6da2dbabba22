from datetime import datetime

def calculate_years_since(date):
    if date is None:
        return 0
    today = datetime.now()
    return today.year - date.year - ((today.month, today.day) < (date.month, date.day))

def years_since_last_promotion(last_promotion_date):
    return calculate_years_since(last_promotion_date)

def is_eligible_for_promotion(last_promotion_date, current_grade_date):
    years_since_promotion = years_since_last_promotion(last_promotion_date)
    years_in_current_grade = calculate_years_since(current_grade_date)
    return years_since_promotion >= 4 and years_in_current_grade >= 4

def days_between_dates(start_date, end_date):
    if start_date is None or end_date is None:
        return None
    return (end_date - start_date).days

def minimum_promotion_period_met(last_promotion_date):
    return years_since_last_promotion(last_promotion_date) >= 4