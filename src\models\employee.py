class Employee:
    def __init__(self, name, employee_id, hire_date, last_promotion_date, current_grade, performance_ratings, admin_status):
        self.name = name
        self.employee_id = employee_id
        self.hire_date = hire_date
        self.last_promotion_date = last_promotion_date
        self.current_grade = current_grade
        self.performance_ratings = performance_ratings  # List of ratings for the last 3 years
        self.admin_status = admin_status

    def duration_in_current_grade(self):
        from datetime import datetime
        today = datetime.now()
        duration = today - self.last_promotion_date
        return duration.days // 365  # Return full years

    def time_since_last_promotion(self):
        from datetime import datetime
        today = datetime.now()
        time_difference = today - self.last_promotion_date
        return time_difference.days // 365  # Return full years

    def is_eligible_for_promotion(self):
        if self.duration_in_current_grade() < 4:
            return False
        
        if self.admin_status.lower() != 'active':
            return False
        
        if all(rating >= 3 for rating in self.performance_ratings):
            return True
        
        return False

    def is_conditionally_eligible(self):
        if self.duration_in_current_grade() < 4:
            return False
        
        if self.admin_status.lower() != 'active':
            return False
        
        if any(rating < 3 for rating in self.performance_ratings):
            return True
        
        return False